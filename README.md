# AroXgo Project

A full-stack application with <PERSON><PERSON>, <PERSON>sia backend and React frontend.

## Bugs Fixed

### Backend Issues Fixed:
1. **Missing Type Definitions**: Added `@types/jsonwebtoken` and `@types/bcrypt` for proper TypeScript support
2. **Unused Import**: Removed unused `verify` import from jsonwebtoken
3. **SQL Syntax Error**: Fixed missing `CREATE` keyword in database schema (`CREATE DATABASE aroxgo_db`)
4. **JWT Sign Calls**: Removed unnecessary `await` from synchronous `sign()` calls
5. **CORS Support**: Added CORS middleware to handle cross-origin requests from frontend
6. **Input Validation**: Added comprehensive validation for signup endpoint:
   - Username length validation (3-50 characters)
   - Password minimum length (6 characters)
   - Email format validation
7. **Database Connection**: Added connection testing and error handling

### Frontend Issues Fixed:
1. **Unused Import**: Removed unused `React` import from AuthContext.tsx
2. **API Tester**: 
   - Fixed default endpoint to point to correct backend URL
   - Added POST method support
   - Improved URL handling for both relative and absolute URLs

### Directory Structure:
- Note: There's a typo in the directory name "Frondend" (should be "Frontend"), but preserved to avoid breaking existing imports

## Project Structure

```
AroXgo/
├── Backend/
│   ├── src/
│   │   ├── index.ts          # Main backend server (requires database)
│   │   └── aroxgo_db.sql     # Database schema
│   ├── mock-server.ts        # Mock server with in-memory storage
│   ├── test-server.ts        # Simple test server (no auth endpoints)
│   ├── create-table.sql      # SQL script to create users table
│   ├── setup-db.js           # Database setup script (has permission issues)
│   └── package.json
└── Frondend/                 # Note: typo in directory name
    ├── src/
    │   ├── App.tsx           # Main React component
    │   ├── AuthContext.tsx   # Authentication context
    │   ├── APITester.tsx     # API testing component
    │   ├── frontend.tsx      # React app entry point
    │   └── index.tsx         # Bun server with frontend routes
    └── package.json
```

## Running the Project

### Prerequisites
- Bun runtime installed
- PostgreSQL database running (for database version only)

### Backend Options

#### Option 1: Mock Server (Recommended for Testing)
```bash
cd Backend

# Install dependencies (already done)
bun install

# Run the mock server (no database required, uses in-memory storage)
bun run mock-server.ts
```

#### Option 2: Database Server (Production)
```bash
cd Backend

# First, set up the database table (run as postgres user):
psql -U postgres -d aroxgo_db -f create-table.sql

# Then run the main server
bun run src/index.ts

# Or run the simple test server (no auth endpoints)
bun run test-server.ts
```

### Frontend
```bash
cd Frondend

# Install dependencies
bun install

# Run development server
bun run dev
```

### Database Setup (Only for Database Server Option)
1. Ensure PostgreSQL is running
2. Create the database and user by running `Backend/src/aroxgo_db.sql` as postgres user
3. Create the users table by running `Backend/create-table.sql` as postgres user:
   ```bash
   psql -U postgres -d aroxgo_db -f Backend/create-table.sql
   ```
4. The backend will connect to: `postgres://aroxgo_user:Aro@2013@localhost:5432/aroxgo_db`

### Current Status
- ✅ **Mock Server**: Fully functional with in-memory storage
- ⚠️ **Database Server**: Requires manual database setup due to permission issues

## API Endpoints

- `GET /` - Hello message
- `POST /signup` - User registration
- `POST /login` - User authentication

## Features

- User authentication with JWT tokens
- Password hashing with bcrypt
- CORS support for cross-origin requests
- Input validation and error handling
- React frontend with authentication context
- API testing component for development
