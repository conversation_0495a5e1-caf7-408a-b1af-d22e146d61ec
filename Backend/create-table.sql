-- Run this as postgres user: psql -U postgres -d aroxgo_db -f create-table.sql

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Grant permissions to aroxgo_user
GRANT ALL PRIVILEGES ON TABLE users TO aroxgo_user;
GRANT USAGE, SELECT ON SEQUENCE users_id_seq TO aroxgo_user;

-- Verify the setup
SELECT 'Table created successfully' as status;
SELECT COUNT(*) as user_count FROM users;
