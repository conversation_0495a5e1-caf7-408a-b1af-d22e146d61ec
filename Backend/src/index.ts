import { Elysia, t } from 'elysia';
import { cors } from '@elysiajs/cors';
import postgres from 'postgres';
import { sign } from 'jsonwebtoken';
import { hash, compare } from 'bcrypt';

const sql = postgres('postgres://aroxgo_user:Aro@2013@localhost:5432/aroxgo_db', {
  onnotice: () => {}, // Suppress notices
});
const JWT_SECRET = process.env.JWT_SECRET || 'supersecretjwtkey'; // Use a strong, environment-variable-based secret in production

// Test database connection
sql`SELECT 1`.then(() => {
  console.log('✅ Database connected successfully');
}).catch((error) => {
  console.error('❌ Database connection failed:', error.message);
});

const app = new Elysia()
  .use(cors())
  .get('/', () => 'Hello from AroXgo Backend!')
  .post('/signup', async ({ body, set }) => {
    const { username, email, password } = body;

    if (!username || !email || !password) {
      set.status = 400;
      return { message: 'Username, email, and password are required' };
    }

    // Basic validation
    if (username.length < 3 || username.length > 50) {
      set.status = 400;
      return { message: 'Username must be between 3 and 50 characters' };
    }

    if (password.length < 6) {
      set.status = 400;
      return { message: 'Password must be at least 6 characters long' };
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      set.status = 400;
      return { message: 'Please provide a valid email address' };
    }

    try {
      const password_hash = await hash(password, 10); // Hash password with salt rounds = 10
      const [user] = await sql`
        INSERT INTO users (username, email, password_hash)
        VALUES (${username}, ${email}, ${password_hash})
        RETURNING id, username, email;
      `;

      const token = sign({ userId: user.id, username: user.username }, JWT_SECRET, { expiresIn: '1h' });

      set.status = 201;
      return { message: 'User registered successfully', user: { id: user.id, username: user.username, email: user.email }, token };
    } catch (error: any) {
      if (error.code === '23505') { // Unique violation error code
        set.status = 409;
        return { message: 'User with this username or email already exists' };
      }
      console.error('Signup error:', error);
      set.status = 500;
      return { message: 'Internal server error' };
    }
  }, {
    body: t.Object({
      username: t.String(),
      email: t.String(),
      password: t.String(),
    })
  })
  .post('/login', async ({ body, set }) => {
    const { email, password } = body;

    if (!email || !password) {
      set.status = 400;
      return { message: 'Email and password are required' };
    }

    try {
      const [user] = await sql`
        SELECT id, username, email, password_hash FROM users WHERE email = ${email};
      `;

      if (!user) {
        set.status = 401;
        return { message: 'Invalid credentials' };
      }

      const isPasswordValid = await compare(password, user.password_hash);

      if (!isPasswordValid) {
        set.status = 401;
        return { message: 'Invalid credentials' };
      }

      const token = sign({ userId: user.id, username: user.username }, JWT_SECRET, { expiresIn: '1h' });

      set.status = 200;
      return { message: 'Login successful', user: { id: user.id, username: user.username, email: user.email }, token };
    } catch (error) {
      console.error('Login error:', error);
      set.status = 500;
      return { message: 'Internal server error' };
    }
  }, {
    body: t.Object({
      email: t.String(),
      password: t.String(),
    })
  })
  .listen(3001);

console.log(
  `🦊 Elysia is running at http://${app.server?.hostname}:${app.server?.port}`
);
