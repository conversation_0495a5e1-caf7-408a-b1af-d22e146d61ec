-- This script should be run by the 'postgres' user.

-- Create a user and a database for our application
CREATE USER aroxgo_user WITH ENCRYPTED PASSWORD 'Aro@2013';
ALTER USER postgres WITH PASSWORD 'new_password_123';
CREATE DATABASE aroxgo_db;
GRANT ALL PRIVILEGES ON DATABASE aroxgo_db TO aroxgo_user;

-- Connect to the new database to install the extension
\c aroxgo_db

-- Install the TimescaleDB extension
CREATE EXTENSION IF NOT EXISTS timescaledb;

-- Grant usage on the extension to the new user
GRANT USAGE ON SCHEMA public TO aroxgo_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO aroxgo_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO aroxgo_user;

-- Create users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
\q
