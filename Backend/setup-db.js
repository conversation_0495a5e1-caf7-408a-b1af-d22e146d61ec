import postgres from 'postgres';

const sql = postgres('postgres://aroxgo_user:Aro@2013@localhost:5432/aroxgo_db');

async function setupDatabase() {
  try {
    console.log('Setting up database...');
    
    // Create users table
    await sql`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    console.log('✅ Users table created successfully');
    
    // Test the table
    const result = await sql`SELECT COUNT(*) FROM users`;
    console.log(`✅ Table test successful. Current user count: ${result[0].count}`);
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await sql.end();
    process.exit(0);
  }
}

setupDatabase();
