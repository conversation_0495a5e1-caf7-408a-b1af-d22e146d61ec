{"name": "@elysiajs/cors", "version": "1.3.3", "description": "Plugin for Elysia that for Cross Origin Requests (CORs)", "author": {"name": "saltyAom", "url": "https://github.com/SaltyAom", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/elysiajs/elysia-cors"}, "main": "./dist/cjs/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/cjs/index.js"}}, "homepage": "https://github.com/elysiajs/elysia-cors", "keywords": ["elysia", "cors"], "license": "MIT", "scripts": {"dev": "bun run --watch example/index.ts", "test": "bun test && npm run test:node", "test:node": "npm install --prefix ./test/node/cjs/ && npm install --prefix ./test/node/esm/ && node ./test/node/cjs/index.js && node ./test/node/esm/index.js", "build": "bun build.ts", "release": "npm run build && npm run test && npm publish --access public"}, "devDependencies": {"@types/bun": "1.1.14", "@types/node": "^20.14.10", "elysia": ">= 1.3.0-exp.45", "eslint": "9.6.0", "tsup": "^8.1.0", "typescript": "^5.5.2"}, "peerDependencies": {"elysia": ">= 1.3.0"}}