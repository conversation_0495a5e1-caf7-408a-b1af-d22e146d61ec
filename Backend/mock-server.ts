import { Elysia, t } from 'elysia';
import { cors } from '@elysiajs/cors';
import { sign } from 'jsonwebtoken';
import { hash, compare } from 'bcrypt';

const JWT_SECRET = process.env.JWT_SECRET || 'supersecretjwtkey';

// In-memory storage for testing (replace with database in production)
const users: Array<{
  id: number;
  username: string;
  email: string;
  password_hash: string;
  created_at: string;
}> = [];

let nextUserId = 1;

const app = new Elysia()
  .use(cors())
  .get('/', () => 'Hello from AroXgo Backend! (Mock Mode - No Database Required)')
  .get('/health', () => ({ 
    status: 'ok', 
    mode: 'mock',
    timestamp: new Date().toISOString(),
    users_count: users.length
  }))
  .post('/signup', async ({ body, set }) => {
    const { username, email, password } = body;

    if (!username || !email || !password) {
      set.status = 400;
      return { message: 'Username, email, and password are required' };
    }

    // Basic validation
    if (username.length < 3 || username.length > 50) {
      set.status = 400;
      return { message: 'Username must be between 3 and 50 characters' };
    }

    if (password.length < 6) {
      set.status = 400;
      return { message: 'Password must be at least 6 characters long' };
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      set.status = 400;
      return { message: 'Please provide a valid email address' };
    }

    try {
      // Check if user already exists
      const existingUser = users.find(u => u.username === username || u.email === email);
      if (existingUser) {
        set.status = 409;
        return { message: 'User with this username or email already exists' };
      }

      const password_hash = await hash(password, 10);
      const user = {
        id: nextUserId++,
        username,
        email,
        password_hash,
        created_at: new Date().toISOString()
      };

      users.push(user);

      const token = sign({ userId: user.id, username: user.username }, JWT_SECRET, { expiresIn: '1h' });

      set.status = 201;
      return { 
        message: 'User registered successfully', 
        user: { id: user.id, username: user.username, email: user.email }, 
        token 
      };
    } catch (error: any) {
      console.error('Signup error details:', {
        message: error.message,
        stack: error.stack
      });
      set.status = 500;
      return { message: 'Internal server error', error: error.message };
    }
  }, {
    body: t.Object({
      username: t.String(),
      email: t.String(),
      password: t.String(),
    })
  })
  .post('/login', async ({ body, set }) => {
    const { email, password } = body;

    if (!email || !password) {
      set.status = 400;
      return { message: 'Email and password are required' };
    }

    try {
      const user = users.find(u => u.email === email);

      if (!user) {
        set.status = 401;
        return { message: 'Invalid credentials' };
      }

      const isPasswordValid = await compare(password, user.password_hash);

      if (!isPasswordValid) {
        set.status = 401;
        return { message: 'Invalid credentials' };
      }

      const token = sign({ userId: user.id, username: user.username }, JWT_SECRET, { expiresIn: '1h' });

      set.status = 200;
      return { 
        message: 'Login successful', 
        user: { id: user.id, username: user.username, email: user.email }, 
        token 
      };
    } catch (error: any) {
      console.error('Login error details:', {
        message: error.message,
        stack: error.stack
      });
      set.status = 500;
      return { message: 'Internal server error', error: error.message };
    }
  }, {
    body: t.Object({
      email: t.String(),
      password: t.String(),
    })
  })
  .listen(3001);

console.log(
  `🦊 Elysia Mock Server is running at http://${app.server?.hostname}:${app.server?.port}`
);
console.log('📝 This is a mock server using in-memory storage for testing purposes');
console.log('🔄 Data will be lost when the server restarts');
