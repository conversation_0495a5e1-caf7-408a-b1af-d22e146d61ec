import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';

// Simple test server without database dependency
const testApp = new Elysia()
  .use(cors())
  .get('/', () => 'Hello from AroXgo Backend!')
  .get('/health', () => ({ status: 'ok', timestamp: new Date().toISOString() }))
  .listen(3001);

console.log(
  `🦊 Test server is running at http://${testApp.server?.hostname}:${testApp.server?.port}`
);
